<template>
  <div
    class="proxy-batchs"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="作业主体">
            <CorporationsSelector
              v-model="conditions.filters.corporationIds"
              style="width: 280px"
              multiple
            />
          </el-form-item>
          <el-form-item label="客户">
            <SupplierCustomersSelector
              v-model="conditions.filters.customer"
              style="width: 280px"
            />
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="作业主体">
            <CorporationsSelector
              v-model="conditions.filters.corporationIds"
              style="width: 280px"
              multiple
            />
          </el-form-item>
          <el-form-item label="客户">
            <SupplierCustomersSelector
              v-model="conditions.filters.customer"
              style="width: 280px"
            />
          </el-form-item>
        </div>
        <div>
          <el-form-item label="合同">
            <ServiceContractsSelector
              v-model="conditions.filters.contractIds"
              style="width: 280px"
              multiple
            />
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="batchId"
        label="代发批次ID"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="salaryStatementId"
        label="工资表ID"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customer"
        label="客户名称"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="businessContract"
        label="服务合同名称"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="corporation"
        label="作业主体名称"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="提交代发时间"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="count"
        label="总人数"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="totalAmount"
        label="实发金额总计"
        width="120"
      ></el-table-column>
      <el-table-column prop="batchStatus" label="状态" width="100">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.batchStatus)]">
            {{ getStatusText(scope.row.batchStatus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import CorporationsSelector from './selector/corporations.vue'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
const client = makeClient()

export default {
  components: {
    CorporationsSelector,
    SupplierCustomersSelector,
    ServiceContractsSelector
  },
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          corporation: '',
          businessContract: '',
          customer: '',
          corporationIds: [],
          contractIds: [],
          batchStatus: null
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions.filters = {
        id: '',
        corporation: '',
        businessContract: '',
        customer: '',
        corporationIds: [],
        contractIds: [],
        batchStatus: null
      }
      this.getList()
    },
    async getList() {
      this.loading = true
      const [err, r] = await client.supplierListProxyBatch({
        body: this.conditions
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    getStatusText(status) {
      const statusMap = {
        CHECK: '核验中',
        PROCESSING: '处理中',
        COMPLETE: '已完成',
        DELETED: '已删除'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        CHECK: 'status-processing',
        PROCESSING: 'status-processing',
        COMPLETE: 'status-check-succ',
        DELETED: 'status-deleted'
      }
      return classMap[status] || 'status-default'
    },
    handleView(row) {
      this.$router.push(`/proxyBatchs/${row.batchId}`)
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-create {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-check-succ {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-check-fail {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-remit {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-fail {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-refund {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-deleted {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
