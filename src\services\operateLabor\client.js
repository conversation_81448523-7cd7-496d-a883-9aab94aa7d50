class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  // 当前登录用户信息
  async supplierProfile(options = {}) {
    const resource = `/api/supplier/profile`
    return this.httpClient.request(resource, options)
  }
  //角色列表
  async listRoles(options = {}) {
    const resource = `/api/supplier/listRole`
    return this.httpClient.request(resource, options)
  }
  // 图形验证码
  async createCaptcha(options = {}) {
    const resource = `/api/public/createCaptcha`
    return this.httpClient.request(resource, options)
  }

  // 发送登录验证码
  async apiSendLoginSms(options = {}) {
    const resource = `/api/public/sendLoginSms`
    return this.httpClient.request(resource, options)
  }
  // 登录
  async login(options = {}) {
    const resource = `/api/public/login`
    return this.httpClient.request(resource, options)
  }
  // 域名获取品牌信息
  async domainInfo(options = {}) {
    const resource = `/api/public/domainInfo`
    return this.httpClient.request(resource, options)
  }
  // 客户列表
  async supplierListCustomer(options = {}) {
    const resource = `/api/supplier/customer/listCustomer`
    return this.httpClient.request(resource, options)
  }

  // 查询平台下客户列表
  async queryCustomerBySupplier(options = {}) {
    const resource = `/api/supplier/customer/queryCustomerBySupplier`
    return this.httpClient.request(resource, options)
  }

  // 新增客户
  async addCustomer(options = {}) {
    const resource = `/api/supplier/customer/addCustomer`
    return this.httpClient.request(resource, options)
  }

  // 查询客户详情
  async queryCustomer(options = {}) {
    const resource = `/api/supplier/customer/queryCustomer`
    return this.httpClient.request(resource, options)
  }

  // 更新客户信息
  async updateCustomer(options = {}) {
    const resource = `/api/supplier/customer/updateCustomer`
    return this.httpClient.request(resource, options)
  }

  // 更新客户状态
  async updateCustomerStatus(options = {}) {
    const resource = `/api/supplier/customer/updateCustomerStatus`
    return this.httpClient.request(resource, options)
  }

  // 菜单
  async supplierGetMenu(options = {}) {
    return Promise.resolve([
      null,
      {
        success: true,
        errorCode: '0',
        data: {
          title: '根节点',
          children: [
            {
              title: '设置',
              children: [
                {
                  title: '平台信息配置',
                  path: '/supplierSettings',
                  children: []
                },
                {
                  title: '角色权限管理',
                  path: '/roles',
                  children: []
                },
                {
                  title: '作业主体管理',
                  path: '/corporations',
                  children: []
                }
              ]
            },
            {
              title: '客户',
              children: [
                {
                  title: '客户信息',
                  path: '/supplierCustomers',
                  children: []
                },
                {
                  title: '服务合同',
                  path: '/serviceContracts',
                  children: []
                }
              ]
            },
            {
              title: '人员',
              children: [
                {
                  title: '人员信息',
                  path: '/supplierLabor',
                  children: []
                },
                {
                  title: '合同管理',
                  children: [
                    {
                      title: '电子合同',
                      path: '/contracts',
                      children: []
                    },
                    {
                      title: '合同模版',
                      path: '/contractTemplates',
                      children: []
                    }
                  ]
                }
              ]
            },
            {
              title: '结算',
              children: [
                {
                  title: '薪酬管理',
                  children: [
                    {
                      title: '薪酬计算',
                      path: '/payrolls',
                      children: []
                    },
                    {
                      title: '代发批次',
                      path: '/proxyBatchs',
                      children: []
                    },
                    {
                      title: '代发订单',
                      path: '/proxyOrders',
                      children: []
                    }
                  ]
                },
                {
                  title: '账单管理',
                  path: '/billingManage',
                  children: []
                },
                {
                  title: '发票管理',
                  path: '/invoices',
                  children: []
                }
              ]
            },
            {
              title: '税务',
              children: [
                {
                  title: '信息报送',
                  children: [
                    {
                      title: '人员信息报送',
                      path: '/personnelInfoSubmission',
                      children: []
                    },
                    {
                      title: '人员收入信息报送',
                      path: '/personnelIncomeInfoSubmission',
                      children: []
                    },
                    {
                      title: '企业信息报送',
                      path: '/enterpriseInfoSubmission',
                      children: []
                    }
                  ]
                },
                {
                  title: '个税申报',
                  path: '/personalIncomeTax',
                  children: []
                },
                {
                  title: '增值税申报',
                  path: '/valueAddedTax',
                  children: []
                },
                {
                  title: '税款缴纳',
                  path: '/taxPaymentVoucher',
                  children: []
                }
              ]
            }
          ]
        }
      }
    ])
    // const resource = `/api/supplier/getMenu`
    // return this.httpClient.request(resource, options)
  }
  // 权限树
  async supplierGetAuthorityTree(options = {}) {
    const resource = `/api/supplier/getAuthorityTree`
    return this.httpClient.request(resource, options)
  }

  // 创建权限
  async createRole(options = {}) {
    const resource = `/api/supplier/addRole`
    return this.httpClient.request(resource, options)
  }
  // 启用/禁用角色
  async disableRole(options = {}) {
    const resource = `/api/supplier/disableRole`
    return this.httpClient.request(resource, options)
  }
  // 删除角色
  async deleteRole(options = {}) {
    const resource = `/api/supplier/deleteRole`
    return this.httpClient.request(resource, options)
  }
  // 编辑角色
  async editRole(options = {}) {
    const resource = `/api/supplier/editRole`
    return this.httpClient.request(resource, options)
  }
  // 角色详情
  async roleDetail(options = {}) {
    const resource = `/api/supplier/roleDetail`
    return this.httpClient.request(resource, options)
  }
  // 获取角色成员
  async getRoleMembers(options = {}) {
    const resource = `/api/supplier/getRoleMembers`
    return this.httpClient.request(resource, options)
  }

  // 服务合同列表
  async supplierListContract(options = {}) {
    const resource = `/api/supplier/contract/listContract`
    return this.httpClient.request(resource, options)
  }

  // 新增服务合同
  async addContract(options = {}) {
    const resource = `/api/supplier/contract/addContract`
    return this.httpClient.request(resource, options)
  }

  // 查询服务合同详情
  async queryContract(options = {}) {
    const resource = `/api/supplier/contract/queryContract`
    return this.httpClient.request(resource, options)
  }

  // 更新服务合同
  async updateContract(options = {}) {
    const resource = `/api/supplier/contract/updateContract`
    return this.httpClient.request(resource, options)
  }

  // 提前终止服务合同
  async terminateContract(options = {}) {
    const resource = `/api/supplier/contract/terminateContract`
    return this.httpClient.request(resource, options)
  }

  // 上传文件
  async uploadFile(options = {}) {
    const resource = `/api/public/uploadFile`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 业务主体列表
  async listCorporation(options = {}) {
    const resource = `/api/supplier/listCorporation`
    return this.httpClient.request(resource, options)
  }

  // 业务主体详情
  async corporationDetail(options = {}) {
    const resource = `/api/supplier/corporationDetail`
    return this.httpClient.request(resource, options)
  }
  //  业务主体配置详情
  async corporationConfigDetail(options = {}) {
    const resource = `/api/supplier/corporationConfigDetail`
    return this.httpClient.request(resource, options)
  }

  // 添加业务主体
  async addCorporation(options = {}) {
    const resource = `/api/supplier/addCorporation`
    return this.httpClient.request(resource, options)
  }

  // 修改业务主体
  async editCorporation(options = {}) {
    const resource = `/api/supplier/editCorporation`
    return this.httpClient.request(resource, options)
  }

  // 添加修改业务主体配置
  async editCorporationBusiness(options = {}) {
    const resource = `/api/supplier/editCorporationBusiness`
    return this.httpClient.request(resource, options)
  }
  // 描述文件
  async describeFile(options = {}) {
    const resource = `/api/public/describeFile`
    return this.httpClient.request(resource, options)
  }
  // 获取可用通道
  async supplierPayChannelList(options = {}) {
    const resource = `/api/supplier/supplierPayChannelList`
    return this.httpClient.request(resource, options)
  }
  // 配置通道参数
  async supplierCorporationConfigPayChannel(options = {}) {
    const resource = `/api/supplier/corporationConfigPayChannel`
    return this.httpClient.request(resource, options)
  }

  // 首次设置密码
  async setPassword(options = {}) {
    const resource = `/api/supplier/setPassword`
    return this.httpClient.request(resource, options)
  }

  // 重置密码
  async resetPassword(options = {}) {
    const resource = `/api/supplier/resetPassword`
    return this.httpClient.request(resource, options)
  }

  // 获取成员
  async supplierGetMembers(options = {}) {
    const resource = `/api/supplier/getMembers`
    return this.httpClient.request(resource, options)
  }

  // 启用/禁用成员
  async supplierDisableMember(options = {}) {
    const resource = `/api/supplier/disableMember`
    return this.httpClient.request(resource, options)
  }

  // 删除成员
  async supplierRemoveMember(options = {}) {
    const resource = `/api/supplier/removeMember`
    return this.httpClient.request(resource, options)
  }

  // 添加成员
  async supplierAddMember(options = {}) {
    const resource = `/api/supplier/addMember`
    return this.httpClient.request(resource, options)
  }

  // 编辑成员
  async supplierEditMember(options = {}) {
    const resource = `/api/supplier/editMember`
    return this.httpClient.request(resource, options)
  }

  // 获取供应商详情
  async supplierDetail(options = {}) {
    const resource = `/api/supplier/supplierDetail`
    return this.httpClient.request(resource, options)
  }

  // 编辑供应商信息
  async editSupplier(options = {}) {
    const resource = `/api/supplier/editSupplier`
    return this.httpClient.request(resource, options)
  }

  // 获取合同模板列表
  async getTemplateList(options = {}) {
    const resource = `/api/supplier/protocol/template/list`
    return this.httpClient.request(resource, options)
  }

  // 检查模板是否可以更新
  async canUpdateTemplate(id, options = {}) {
    options.method = 'GET'
    const resource = `/api/supplier/protocol/template/canUpdate?id=${id}`
    return this.httpClient.request(resource, options)
  }

  // 更新模板状态
  async updateTemplateStatus(options = {}) {
    const resource = `/api/supplier/protocol/template/updateTempStatus`
    return this.httpClient.request(resource, options)
  }

  // 获取模板详情
  async getTemplateDetail(id, options = {}) {
    const resource = `/api/supplier/protocol/template/get?id=${id}`
    return this.httpClient.request(resource, options)
  }

  // 创建/更新模板
  async createTemplate(options = {}) {
    const resource = `/api/supplier/protocol/template/create`
    return this.httpClient.request(resource, options)
  }
  //创建模板第二步
  async setTemplate(options = {}) {
    const resource = `/api/supplier/protocol/template/setTemplate`
    return this.httpClient.request(resource, options)
  }

  // 获取模板详情
  async getTemplateDetail(tempId, options = {}) {
    const resource = `/api/supplier/protocol/template/getTemplateDetail?tempId=${tempId}`
    return this.httpClient.request(resource, options)
  }

  async supplierLaborList(options = {}) {
    const resource = `/api/supplier/labor/list`
    return this.httpClient.request(resource, options)
  }

  // 根据客户和业务主体获取合同列表
  async listContractByCustomerAndCorporation(options = {}) {
    const resource = `/api/supplier/contract/listContractByCustomerAndCorporation`
    return this.httpClient.request(resource, options)
  }

  // 添加人员
  async addLabor(options = {}) {
    const resource = `/api/supplier/labor/create`
    return this.httpClient.request(resource, options)
  }

  // 获取模板字段
  async getTemplateFields(options = {}) {
    const resource = `/api/protocol/template/fields`
    return this.httpClient.request(resource, options)
  }

  // 提交模板配置
  async commitTemplate(options = {}) {
    const resource = `/api/protocol/template/commit`
    return this.httpClient.request(resource, options)
  }

  // 获取人员详情
  async getLaborDetail(options = {}) {
    const resource = `/api/supplier/labor/get/${options.pathParams.id}`
    return this.httpClient.request(resource, options)
  }

  // 更新人员信息
  async updateLabor(options = {}) {
    const resource = `/api/supplier/labor/update`
    return this.httpClient.request(resource, options)
  }

  // 下载人员导入模板
  async downloadLaborTemplate(options = {}) {
    const resource = `/api/supplier/labor/download/template`
    return this.httpClient.request(resource, {
      ...options,
      headers: { 'content-type': 'application/octet-stream' }
    })
  }

  // 批量上传人员信息校验
  async batchUploadLaborCheck(options = {}) {
    const resource = `/api/supplier/labor/download/check`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 导出错误数据
  async exportLaborErrorLog(uuid, options = {}) {
    const resource = `/api/supplier/labor/importVerifyErrorLog/${uuid}`
    return this.httpClient.request(resource, {
      ...options,
      method: 'GET',
      headers: { 'content-type': 'application/octet-stream' }
    })
  }
  // 根据域名获取配置信息
  async getDomainInfo(options = {}) {
    const resource = `/api/public/domainInfo`
    return this.httpClient.request(resource, options)
  }

  // 个税申报列表
  async personalTaxList(options = {}) {
    const resource = `/api/supplier/personaltax/list`
    return this.httpClient.request(resource, options)
  }

  // 生成个税申报表
  async addPersonalTax(options = {}) {
    const resource = `/api/supplier/personaltax/add`
    return this.httpClient.request(resource, options)
  }

  // 查询个税申报详情
  async queryPersonalTax(options = {}) {
    const resource = `/api/supplier/personaltax/query`
    return this.httpClient.request(resource, options)
  }

  // 增值税申报列表
  async valueAddedTaxList(options = {}) {
    const resource = `/api/supplier/valueaddedtax/list`
    return this.httpClient.request(resource, options)
  }

  // 生成增值税申报表
  async addValueAddedTax(options = {}) {
    const resource = `/api/supplier/valueaddedtax/add`
    return this.httpClient.request(resource, options)
  }

  // 查询增值税申报详情
  async queryValueAddedTax(options = {}) {
    const resource = `/api/supplier/valueaddedtax/query`
    return this.httpClient.request(resource, options)
  }

  // 更新个税申报状态
  async updatePersonalTaxStatus(options = {}) {
    const resource = `/api/supplier/personaltax/updateTaxStatus`
    return this.httpClient.request(resource, options)
  }

  // 更新增值税申报状态
  async updateValueAddedTaxStatus(options = {}) {
    const resource = `/api/supplier/valueaddedtax/updateTaxStatus`
    return this.httpClient.request(resource, options)
  }

  // 税款缴纳凭证列表
  async taxPaymentVoucherList(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/list`
    return this.httpClient.request(resource, options)
  }

  // 新增税款缴纳凭证
  async addTaxPaymentVoucher(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/add`
    return this.httpClient.request(resource, options)
  }

  // 查询税款缴纳凭证详情
  async queryTaxPaymentVoucher(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/query`
    return this.httpClient.request(resource, options)
  }

  // 获取文件信息
  async describeFile(options = {}) {
    const resource = `/api/public/describeFile`
    return this.httpClient.request(resource, options)
  }

  // 人员信息报送列表
  async infoLaborList(options = {}) {
    const resource = `/api/supplier/infolabor/list`
    return this.httpClient.request(resource, options)
  }

  // 生成人员信息报送表
  async addInfoLabor(options = {}) {
    const resource = `/api/supplier/infolabor/add`
    return this.httpClient.request(resource, options)
  }

  // 下载人员信息报送表
  async downloadInfoLabor(options = {}) {
    const resource = `/api/supplier/infolabor/download`
    return this.httpClient.request(resource, options)
  }

  // 人员收入信息报送列表
  async infoIncomeList(options = {}) {
    const resource = `/api/supplier/infoincome/list`
    return this.httpClient.request(resource, options)
  }

  // 生成人员收入信息报送表
  async addInfoIncome(options = {}) {
    const resource = `/api/supplier/infoincome/add`
    return this.httpClient.request(resource, options)
  }

  // 下载人员收入信息报送表
  async downloadInfoIncome(options = {}) {
    const resource = `/api/supplier/infoincome/download`
    return this.httpClient.request(resource, options)
  }

  // 企业信息报送列表
  async infoEnterpriseList(options = {}) {
    const resource = `/api/supplier/infoenterprise/list`
    return this.httpClient.request(resource, options)
  }

  // 生成企业信息报送表
  async addInfoEnterprise(options = {}) {
    const resource = `/api/supplier/infoenterprise/add`
    return this.httpClient.request(resource, options)
  }

  // 下载企业信息报送表
  async downloadInfoEnterprise(options = {}) {
    const resource = `/api/supplier/infoenterprise/download`
    return this.httpClient.request(resource, options)
  }

  // 分页查询账单列表
  async apiSupplierBillsList(options = {}) {
    const resource = `/api/supplier/bills/list`
    return this.httpClient.request(resource, options)
  }

  // 生成账单
  async apiSupplierBillsGenerate(options = {}) {
    const resource = `/api/supplier/bills/generate`
    return this.httpClient.request(resource, options)
  }

  // 获取账单详情
  async apiSupplierBillsDetail(options = {}) {
    const resource = `/api/supplier/bills/detail`
    return this.httpClient.request(resource, options)
  }

  // 提交账单确认
  async apiSupplierBillsSubmit(options = {}) {
    const resource = `/api/supplier/bills/submit`
    return this.httpClient.request(resource, options)
  }

  // 确认账单
  async apiSupplierBillsConfirm(options = {}) {
    const resource = `/api/supplier/bills/confirm`
    return this.httpClient.request(resource, options)
  }

  // 删除账单
  async apiSupplierBillsDelete(options = {}) {
    const resource = `/api/supplier/bills/delete`
    return this.httpClient.request(resource, options)
  }

  // 下载其他费用导入模板
  async apiSupplierBillsImportTemplate(options = {}) {
    options.method = 'GET'
    const resource = `/api/supplier/bills/other-fees/import/template`
    return this.httpClient.request(resource, options)
  }

  // 导入其他费用数据
  async apiSupplierBillsImportPreview(options = {}) {
    const resource = `/api/supplier/bills/other-fees/import/preview`
    return this.httpClient.request(resource, options)
  }

  // 获取账单薪酬明细
  async apiSupplierBillSalaryDetails(options = {}) {
    const resource = `/api/supplier/bills/salary-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单管理费明细
  async apiSupplierBillsManagementFeeDetails(options = {}) {
    const resource = `/api/supplier/bills/management-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单其他费用明细
  async apiSupplierBillsOtherFeeDetails(options = {}) {
    const resource = `/api/supplier/bills/other-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 电子合同列表
  async supplierProtocolList(options = {}) {
    const resource = `/api/supplier/protocol/list`
    return this.httpClient.request(resource, options)
  }

  // 发起签署
  async supplierProtocolSignInit(options = {}) {
    const resource = `/api/supplier/protocol/signInit`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算列表
  async supplierSalaryListPayroll(options = {}) {
    const resource = `/api/supplier/salary/listPayroll`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算详情
  async supplierSalaryListPayrollDetail(options = {}) {
    const resource = `/api/supplier/salary/listPayrollDetail`
    return this.httpClient.request(resource, options)
  }

  // 上期收入与减除
  async supplierSalaryListPreviousIncomeDeduction(options = {}) {
    const resource = `/api/supplier/salary/listPreviousIncomeDeduction`
    return this.httpClient.request(resource, options)
  }

  // 创建工资表
  async supplierSalaryAddPayroll(options = {}) {
    const resource = `/api/supplier/salary/addPayroll`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 下载模板
  async supplierSalaryDownloadTemplate(taxCalculationMethod, options = {}) {
    const resource = `/api/supplier/salary/download/template?taxCalculationMethod=${taxCalculationMethod}`
    return this.httpClient.request(resource, options)
  }

  // 创建代发批次
  async supplierCreateBatch(options = {}) {
    const resource = `/api/supplier/createBatch`
    return this.httpClient.request(resource, options)
  }

  // 代发记录列表
  async supplierListProxyBatch(options = {}) {
    const resource = `/api/supplier/listProxyBatch`
    return this.httpClient.request(resource, options)
  }

  // 查询代发批次
  async supplierListProxy(options = {}) {
    const resource = `/api/supplier/listProxy`
    return this.httpClient.request(resource, options)
  }

  // 查询批次汇总信息
  async supplierBatchSummary(options = {}) {
    const resource = `/api/supplier/proxy/batchSummary`
    return this.httpClient.request(resource, options)
  }

  // 确认支付
  async supplierConfirmPay(options = {}) {
    const resource = `/api/supplier/confirmPay`
    return this.httpClient.request(resource, options)
  }

  // 删除批次
  async supplierDeleteBatch(options = {}) {
    const resource = `/api/supplier/deleteBatch`
    return this.httpClient.request(resource, options)
  }

  // 发票列表
  async supplierInvoicesList(options = {}) {
    const resource = `/api/supplier/invoices/list`
    return this.httpClient.request(resource, options)
  }

  // OCR识别并验证
  async supplierLaborOcrVerify(options = {}) {
    const resource = `/api/supplier/labor/ocrVerify`
    return this.httpClient.request(resource, options)
  }

  // 活体人脸识别
  async apiFaceAuth(options = {}) {
    const resource = `/api/public/faceAuth`
    return this.httpClient.request(resource, options)
  }

  // 人员待签署列表查询
  async supplierProtocolGetLaborContract(options = {}) {
    const resource = `/api/supplier/protocol/getLaborContract`
    return this.httpClient.request(resource, options)
  }

  // h5个人签署
  async supplierProtocolSign(options = {}) {
    const resource = `/api/supplier/protocol/sign`
    return this.httpClient.request(resource, options)
  }
  // 发票-确认开票
  async supplierInvoicesUploadFile(options = {}) {
    const resource = `/api/supplier/invoices/upload-file`
    return this.httpClient.request(resource, options)
  }

  // 发票-退回
  async supplierInvoicesReturn(options = {}) {
    const resource = `/api/supplier/invoices/return`
    return this.httpClient.request(resource, options)
  }

  // 发票-预设信息
  async supplierInvoicesPresetInfo(options = {}) {
    const resource = `/api/supplier/invoices/preset-info`
    return this.httpClient.request(resource, options)
  }
  // 发票-申请开票
  async supplierInvoicesApply(options = {}) {
    const resource = `/api/supplier/invoices/apply`
    return this.httpClient.request(resource, options)
  }

  // 发票-详情
  async supplierInvoicesDetail(options = {}) {
    const resource = `/api/supplier/invoices/detail`
    return this.httpClient.request(resource, options)
  }

  // // 人员待签署列表查询
  // async supplierProtocolGetLaborContract(options = {}) {
  //   const resource = `/api/supplier/protocol/getLaborContract`
  //   return this.httpClient.request(resource, options)
  // }
}

export default Client
