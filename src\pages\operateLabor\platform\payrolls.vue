<template>
  <div
    class="payrolls-container"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- 搜索区域 -->
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="作业主体">
            <CorporationsSelector
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请输入作业主体"
              style="width: 280px"
            />
          </el-form-item>
          <el-form-item label="客户">
            <SupplierCustomersSelector
              v-model="conditions.filters.customerId"
              placeholder="请输入客户名称"
              style="width: 280px"
            />
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>
        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="作业主体">
            <CorporationsSelector
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请输入作业主体"
              style="width: 280px"
            />
          </el-form-item>
          <el-form-item label="客户">
            <SupplierCustomersSelector
              v-model="conditions.filters.customerId"
              placeholder="请输入客户名称"
              style="width: 280px"
            />
          </el-form-item>
        </div>
        <div>
          <el-form-item label="合同">
            <ServiceContractsSelector
              v-model="conditions.filters.contractId"
              placeholder="请输入合同名称"
              style="width: 280px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="算税中" value="CALCULATING"></el-option>
              <el-option label="待确认" value="UNCONFIRMED"></el-option>
              <el-option label="已确认" value="CONFIRMED"></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button @click="handleImport">上期收入减除导入</el-button>
      <el-button type="primary" @click="handleAdd">新增工资表</el-button>
    </div>

    <!-- 数据列表 -->
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column prop="id" label="工资表ID" width="100"></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户名称"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="contractName"
        label="服务合同合同名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="taxPeriod"
        label="税款所属期"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="totalPeople"
        label="总人数"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="totalPayable"
        label="应发金额总计"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="totalIncomeTax"
        label="个税预缴额总计"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="totalVat"
        label="增值税应纳税额总计"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="totalSurtax"
        label="附加税应纳税额总计"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="netPaymentTotal"
        label="实发金额总计"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="taxDeclarationMonth"
        label="个税申报月"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="uploadTime"
        label="上传时间"
        width="180"
      ></el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="280">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button type="text" size="small" @click="handleDownload(scope.row)"
            >下载</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleConfirm(scope.row)"
            v-if="scope.row.status === 'UNCONFIRMED'"
            >确认算税结果</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.row)"
            v-if="scope.row.status !== 'CONFIRMED'"
            >删除</el-button
          >
          <el-button type="text" size="small" @click="handleSubmit(scope.row)"
            >提交代发</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    >
    </el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import CorporationsSelector from './selector/corporations.vue'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'

const client = makeClient()

export default {
  name: 'Payrolls',
  components: {
    CorporationsSelector,
    SupplierCustomersSelector,
    ServiceContractsSelector
  },
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          supplierCorporationId: null,
          customerId: [],
          contractId: '',
          status: null
        }
      },
      data: [],
      loading: true,
      total: 0,
      fullShown: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const [err, r] = await client.supplierSalaryListPayroll({
          body: this.conditions
        })
        if (err) {
          handleError(err)
          return
        }
        this.data = r.data.list || []
        this.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions.filters = {
        supplierCorporationId: null,
        customerId: '',
        contractId: '',
        status: null
      }
      this.onSearch()
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    getStatusText(status) {
      const statusMap = {
        CALCULATING: '算税中',
        UNCONFIRMED: '待确认',
        CONFIRMED: '已确认'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        CALCULATING: 'status-calculating',
        UNCONFIRMED: 'status-unconfirmed',
        CONFIRMED: 'status-confirmed'
      }
      return classMap[status] || 'status-default'
    },
    handleImport() {
      this.$router.push('/previousIncomeDeductions')
    },
    handleAdd() {
      this.$router.push('/payrolls/new')
    },
    handleView(row) {
      this.$router.push({
        path: `/payrolls/${row.id}`,
        query: {
          customerName: row.customerName,
          contractName: row.contractName,
          supplierCorporationName: row.supplierCorporationName,
          taxPeriod: row.taxPeriod,
          totalPeople: row.totalPeople,
          totalPayable: row.totalPayable,
          netPaymentTotal: row.netPaymentTotal
        }
      })
    },
    handleDownload(row) {
      this.$message.info(`下载工资表 ${row.id}`)
    },
    handleConfirm(row) {
      this.$confirm('确认算税结果后将无法修改，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$message.info(`确认算税结果 ${row.id}`)
        })
        .catch(() => {})
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该工资表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$message.info(`删除工资表 ${row.id}`)
        })
        .catch(() => {})
    },
    async handleSubmit(row) {
      // if (!row.id) {
      //   handleError({ message: '请选择工资表' })
      //   return
      // }

      // const [err, r] = await client.supplierCreateBatch({
      //   body: {
      //     id: row.id
      //   }
      // })
      // if (err) {
      //   handleError(err)
      //   return
      // }

      // const batchId = r.data * 1
      const batchId = 26
      // 跳转到代发批次详情页面
      this.$router.push(`/proxyBatch/${batchId}`)
    }
  }
}
</script>

<style scoped></style>
