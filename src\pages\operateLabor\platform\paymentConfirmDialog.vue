<template>
  <el-dialog
    title="支付验证"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div style="margin-top: 20px;">
      <div style="margin-bottom: 30px; font-size: 15px; display: flex; align-items: center; gap: 15px">
        <div style="width: 150px; text-align: right">
          <span style="color: #606266">联系人手机号：</span>
        </div>
        <span style="color: #303133; font-weight: 500">{{ formatPhone(cellPhone) }}</span>
      </div>

      <!-- 图形验证码 -->
      <div style="margin-bottom: 25px; display: flex; align-items: center; gap: 15px">
        <div style="width: 150px; text-align: right">
          <span style="color: #f56c6c">*</span>
          <span style="color: #606266; font-size: 15px">图形验证码：</span>
        </div>
        <el-input
          v-model="captcha.answer"
          placeholder="请输入图形验证码"
          maxlength="4"
          style="width: 180px"
          @input="handleCaptchaInput"
        />
        <img
          :src="captchaUrl"
          alt="验证码"
          style="width: 120px; height: 36px; cursor: pointer; border: 1px solid #dcdfe6; border-radius: 4px"
          @click="refreshCaptcha"
        />
      </div>

      <!-- 短信验证码 -->
      <div style="margin-bottom: 25px; display: flex; align-items: center; gap: 15px">
        <div style="width: 150px; text-align: right">
          <span style="color: #f56c6c">*</span>
          <span style="color: #606266; font-size: 15px">短信验证码：</span>
        </div>
        <el-input
          v-model="smsCode"
          placeholder="请输入短信验证码"
          maxlength="6"
          style="width: 180px"
        />
        <el-button
          type="primary"
          :disabled="!canSendSms || smsCountdown > 0"
          :loading="sendingSms"
          @click="sendSmsCode"
          style="width: 120px"
        >
          {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
        </el-button>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="confirming" @click="handleConfirm">
        确认支付
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'PaymentConfirmDialog',
  props: {
    cellPhone: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      captcha: {
        token: '',
        answer: ''
      },
      smsCode: '',
      smsToken: '',
      smsCountdown: 0,
      sendingSms: false,
      confirming: false,
      smsTimer: null
    }
  },
  computed: {
    captchaUrl() {
      return `${window.env?.apiPath}/api/public/captcha?token=${encodeURIComponent(this.captcha.token)}`
    },
    canSendSms() {
      return this.captcha.token && this.captcha.answer && this.captcha.answer.length === 4
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
      this.refreshCaptcha()
    },
    
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    
    resetForm() {
      this.captcha = { token: '', answer: '' }
      this.smsCode = ''
      this.smsToken = ''
      this.smsCountdown = 0
      this.sendingSms = false
      this.confirming = false
      if (this.smsTimer) {
        clearInterval(this.smsTimer)
        this.smsTimer = null
      }
    },
    
    async refreshCaptcha() {
      const [err, r] = await client.createCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.captcha.token = r.data
      this.captcha.answer = ''
    },
    
    handleCaptchaInput(value) {
      this.captcha.answer = value
    },
    
    async sendSmsCode() {
      if (!this.canSendSms) {
        this.$message.warning('请先完成图形验证码')
        return
      }
      
      this.sendingSms = true
      const [err, r] = await client.sendOtp({
        body: {
          receiver: this.cellPhone,
          captchaToken: this.captcha.token,
          captchaAnswer: this.captcha.answer
        }
      })
      
      this.sendingSms = false
      
      if (err) {
        handleError(err)
        this.refreshCaptcha()
        return
      }
      
      this.smsToken = r.data.token
      this.startCountdown()
      this.$message.success('验证码发送成功')
    },
    
    startCountdown() {
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },
    
    async handleConfirm() {
      if (!this.smsCode) {
        this.$message.warning('请输入短信验证码')
        return
      }
      
      if (!this.smsToken) {
        this.$message.warning('请先获取短信验证码')
        return
      }
      
      this.confirming = true
      this.$emit('confirm', {
        smsCode: this.smsCode,
        smsToken: this.smsToken
      })
    },
    
    confirmComplete() {
      this.confirming = false
      this.handleClose()
    },
    
    confirmError() {
      this.confirming = false
    },
    
    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
  },
  
  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
