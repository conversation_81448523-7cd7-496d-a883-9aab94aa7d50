<template>
  <div
    class="proxy-batch-detail"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- Part 1: Data Statistics -->
    <div style="margin-bottom: 20px; flex: 0">
      <el-row :gutter="20" v-loading="loadingSummary">
        <el-col :span="8">
          <div
            class="statistic-card"
            style="border: 1px solid #67c23a; padding: 20px; border-radius: 5px"
          >
            <div class="statistic-title">校验成功订单（笔数/总金额）</div>
            <div class="statistic-content">
              <span style="font-size: 20px; color: #67c23a"
                >{{ summaryData.checkSuccCount || 0 }}笔</span
              >
              <span style="margin: 0 10px">|</span>
              <span style="font-size: 20px; color: #67c23a"
                >{{ formatCurrency(summaryData.checkSuccAmount) }}元
                (实得)</span
              >
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div
            class="statistic-card"
            style="border: 1px solid #e6a23c; padding: 20px; border-radius: 5px"
          >
            <div class="statistic-title">校验中订单（笔数/总金额）</div>
            <div class="statistic-content">
              <span style="font-size: 20px; color: #e6a23c"
                >{{ summaryData.createCount || 0 }}笔</span
              >
              <span style="margin: 0 10px">|</span>
              <span style="font-size: 20px; color: #e6a23c"
                >{{ formatCurrency(summaryData.createAmount) }}元 (实得)</span
              >
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div
            class="statistic-card"
            style="border: 1px solid #f56c6c; padding: 20px; border-radius: 5px"
          >
            <div class="statistic-title">校验失败订单（笔数/总金额）</div>
            <div class="statistic-content">
              <span style="font-size: 20px; color: #f56c6c"
                >{{ summaryData.checkFailCount || 0 }}笔</span
              >
              <span style="margin: 0 10px">|</span>
              <span style="font-size: 20px; color: #f56c6c"
                >{{ formatCurrency(summaryData.checkFailAmount) }}元
                (实得)</span
              >
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- Part 2: Batch Information and Actions -->
    <div
      style="
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 12px;
        flex: 0;
      "
      v-loading="loadingSummary"
    >
      <span>代发批次ID：{{ summaryData.batchId }}</span>
      <span>工资表ID：{{ summaryData.salaryStatementId }}</span>
      <span>作业主体：{{ summaryData.corporation }}</span>
      <span>客户：{{ summaryData.customer }}</span>
      <span>服务合同：{{ summaryData.businessContract }}</span>
      <div style="flex: 1; text-align: right">
        <el-button @click="handleDeleteBatch">删除批次</el-button>
        <el-button type="primary" @click="handleUpdateStatus"
          >订单状态更新</el-button
        >
        <el-button type="primary" @click="handleConfirmPayment"
          >确认支付</el-button
        >
      </div>
    </div>

    <!-- Part 3: Details Table -->
    <el-table
      v-loading="loadingList"
      size="small"
      :data="listData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="代发订单ID"
        width="100"
      ></el-table-column>
      <el-table-column prop="name" label="姓名" width="100"></el-table-column>
      <el-table-column
        prop="idCard"
        label="身份证号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="cellphone"
        label="手机号"
        width="120"
      ></el-table-column>
      <el-table-column prop="amount" label="应发金额" width="120">
        <template slot-scope="scope">{{
          formatCurrency(scope.row.amount)
        }}</template>
      </el-table-column>
      <el-table-column prop="tax" label="本次预扣预缴个税" width="150">
        <template slot-scope="scope">{{
          formatCurrency(scope.row.tax)
        }}</template>
      </el-table-column>
      <el-table-column prop="vatSurtax" label="增值税增值附件税" width="150">
        <template slot-scope="scope">{{
          formatCurrency(scope.row.vatSurtax)
        }}</template>
      </el-table-column>
      <el-table-column prop="vatRepayment" label="补缴增值税" width="120">
        <template slot-scope="scope">{{
          formatCurrency(scope.row.vatRepayment)
        }}</template>
      </el-table-column>
      <el-table-column prop="actualAmount" label="实发金额" width="120">
        <template slot-scope="scope">{{
          formatCurrency(scope.row.actualAmount)
        }}</template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100"></el-table-column>
      <el-table-column
        prop="lastErrorInfo"
        label="错误信息"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="text-align: right; margin-top: 10px; flex: 0"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ProxyBatchDetail',
  data() {
    return {
      batchId: null,
      summaryData: {},
      listData: [],
      loadingSummary: false,
      loadingList: false,
      total: 0,
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        filters: {
          batchId: 0
        }
      }
    }
  },
  async created() {
    this.batchId = this.$route.params.id
    if (this.batchId) {
      this.conditions.filters.batchId = this.batchId
      this.getSummary()
      this.getList()
    }
  },
  methods: {
    async getSummary() {
      this.loadingSummary = true
      const [err, res] = await client.supplierBatchSummary({
        body: { id: this.batchId }
      })
      this.loadingSummary = false
      if (err) {
        handleError(err)
        return
      }
      this.summaryData = res.data || {}
    },
    async getList() {
      this.loadingList = true
      const [err, res] = await client.supplierListProxy({
        body: this.conditions
      })
      this.loadingList = false
      if (err) {
        handleError(err)
        return
      }
      this.listData = res.data.list || []
      this.total = res.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    formatCurrency(value) {
      if (value == null) return '0.00'
      return value.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    async handleDeleteBatch() {
      try {
        await this.$confirm('确定要删除该批次吗？此操作不可逆。', '警告', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return
      }

      const [err] = await client.supplierDeleteBatch({
        body: { id: this.batchId * 1 }
      })

      if (err) {
        handleError(err)
        return
      }

      this.$message.success('批次删除成功')
      this.$router.push('/proxyBatchs')
    },
    async handleUpdateStatus() {
      // TODO: Call actual API for updating status
      console.log('call update status api')
      this.$message.info('状态更新请求已发送')
    },
    async handleConfirmPayment() {
      try {
        await this.$confirm('确定要支付该批次吗？', '确认支付', {
          confirmButtonText: '确定支付',
          cancelButtonText: '取消',
          type: 'success'
        })
      } catch (error) {
        return
      }

      const [err] = await client.supplierConfirmPay({
        body: { id: this.batchId * 1 }
      })

      if (err) {
        handleError(err)
        return
      }

      this.$message.success('支付已确认')
      this.getSummary()
      this.getList()
    }
  }
}
</script>

<style scoped>
.statistic-card {
  text-align: center;
}
.statistic-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}
.statistic-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-form .el-form-item {
  margin-bottom: 0;
}
</style>
